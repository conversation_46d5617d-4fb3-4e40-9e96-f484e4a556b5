﻿  UniTextAPIExport.cpp
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniFontAtlasEntry.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../UniText/src/UniTextAPIExport.cpp”)
  
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTexture.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../UniText/src/UniTextAPIExport.cpp”)
  
  UniTextGenerator.cpp
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniFontAtlasEntry.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../UniText/src/UniTextGenerator.cpp”)
  
E:\trunk_base\Young\UniTextGit\UniTextOrigin\UniText\src\UniTexture.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../../UniText/src/UniTextGenerator.cpp”)
  
  正在生成代码...
  Auto build dll exports
    正在创建库 E:/trunk_base/Young/UniTextGit/UniTextOrigin/build/UniText/Debug/libUniText.lib 和对象 E:/trunk_base/Young/UniTextGit/UniTextOrigin/build/UniText/Debug/libUniText.exp
  libUniText.vcxproj -> E:\trunk_base\Young\UniTextGit\UniTextOrigin\build\UniText\Debug\libUniText.dll
  'pwsh.exe' 不是内部或外部命令，也不是可运行的程序
  或批处理文件。
