#ifndef __UniTextGenerator_h__
#define __UniTextGenerator_h__

#include "UniFont.h"
#include <memory>
#include <stack>
#include <unordered_map>
#include <optional>
#include <variant>
#include <array>

NAMESPACE_BEGIN

/// <summary>
/// UniTextGenerator.h
/// 负责string->Unicode转换，负责调用Font接口，生成对应quad
/// *可能负责字符排列
/// *可能负责与C#交互
/// </summary>
class UniTextGenerator
{
public:
    using UniString = std::basic_string<char, std::char_traits<char>, std::pmr::polymorphic_allocator<char>>;

    enum DirtyType : unsigned short
    {
        None = 0,
        Positions   = 1 << 0,
        UVs         = 1 << 1,
        Colors      = 1 << 2,
        Vertices    = Positions | UVs | Colors,

        Alignment   = 1 << 3,

        LineSpacing = 1 << 5,
        CharSpacing = 1 << 6,

        All         = 0xffff
    };

    struct Vertex
    {
        Color color;
        Vector2f position;
        Vector4f uv;
    };

    struct MeshData
    {
        std::vector<Color> colors;
        std::vector<Vector2f> vertices;
        std::vector<Vector4f> uvs;

        void Reserve(int capacity)
        {
            colors.reserve(capacity);
            vertices.reserve(capacity);
            uvs.reserve(capacity);
        }

        void Clear()
        {
            colors.clear();
            vertices.clear();
            uvs.clear();
        }
    };

    /// <summary>
    /// Pen, Letter, Word, Line are all necessary for character layout.
    /// </summary>
    struct Letter
    {
        Vector2f advance{ 0.0f, 0.0f };
        int vertexIdx;
    };

    struct Word
    {
        int startIndex{ -1 };
        int endIndex{ -1 };
    };

    struct Line
    {
        int startIndex{ -1 };
        // endIndex = nextLine.startIndex - thisLine.startIndex;
        int endIndex{ -1 };
        float width{ 0.0f };
        float height{ 0.0f };
        float offset{ 0.0f };    // x offset : for alignment
        int maxFontSize{ 0 };
    };

    struct Pen
    {
        uint16 prevUnicode;
        int currentCharIdx;
        Word currentWord;
        // x,y is the next character's bottom left corner.
        // That makes y the baseline height.
        float x;
        float y;
        float lineSpacing;
        float minX, minY, maxX, maxY;
        // start offset
        Vector2f offset;

        void Reset() {
            prevUnicode = 0;
            currentCharIdx = 0;
            currentWord = Word();
            x = 0.0f;
            y = 0.0f;
            lineSpacing = 0.0f;
            minX = minY = maxX = maxY = 0.0f;
            offset = Vector2f(0.0f, 0.0f);
        }
    };

public:
    UniTextGenerator();
    ~UniTextGenerator();

    void SetActive(bool active);
    bool IsActive() const noexcept { return m_IsActive; }

#pragma region feature
    DEFINE_BITS_PROPERTY(RichText, 0, m_Features, SetDirty())
    /// <summary>
    /// AutoSize is a bit different from Unity's implementation
    /// Only do autosizing when exceeds vertical bounds.
    /// </summary>
    DEFINE_BITS_PROPERTY(AutoSize, 1, m_Features, SetDirty())
    DEFINE_BITS_PROPERTY(Kerning,  2, m_Features, SetDirty())
#pragma endregion

    void SetText(const char* text);
    void AppendText(const char* text);

    void SetFont(const char* fontName);
    void SetFont(UniFont* uniFont);

    void SetColor(const Color& color) { if (m_BaseColor == color) return; m_BaseColor = color; SetDirty(); }
    Color GetColor() const { return m_BaseColor; }

    void SetHorizontalOverflow(TextOverflow textOverflow) { if (m_HorizontalOverflow == textOverflow) return; m_HorizontalOverflow = textOverflow; SetDirty(); }
    TextOverflow GetHorizontalOverflow() const { return m_HorizontalOverflow; }
    void SetVerticalOverflow(TextOverflow textOverflow) { if (m_VerticalOverflow == textOverflow) return; m_VerticalOverflow = textOverflow; SetDirty(); }
    TextOverflow GetVerticalOverflow() const { return m_VerticalOverflow; }
    void SetBounds(float width, float height);
    inline const GlyphBounds& GetBounds() const { return m_Bounds; }
    inline void GetBounds(GlyphBounds& bounds) const { bounds = m_Bounds; }

    void SetFontSize(int16 fontSize);
    inline int16 GetFontSize() const { return m_BaseFontSize; }

    void SetHorizontalAlignment(TextAlignment alignment);
    TextAlignment GetHorizontalAlignment() const { return m_HorizontalAlignment; }
    void SetVerticalAlignment(TextAlignment alignment);
    TextAlignment GetVerticalAlignment() const { return m_VerticalAlignment; }
    void SetPivot(Vector2f pivot);
    Vector2f GetPivot() const { return m_Pivot; }
    void SetStyle(/*TODO*/);
    void SetLineSpacing(float lineSpacing);
    float GetLineSpacing() const { return m_BaseLineSpacing; }
    void SetCharacterSpacing(float charSpacing);
    float GetCharacterSpacing() const { return m_BaseCharSpacing; }

    void SetStrokeSize(float strokeSize);
    float GetStrokeSize() const { return m_BaseStrokeSize; }

    UniFont* GetFont() const { return m_Font; }

    inline const ITexture* GetFontAtlas(int32 index = 0) const
    {
        return m_Font->GetFontAtlas(m_BaseFontSize, index);
    }

    inline const IUniGlyphPacker* GetAtlasPacker(int index = 0) const
    {
        return m_Font->GetAtlasPacker(m_BaseFontSize, index);
    }

    inline const MeshData& GetMeshData() const { return m_MeshData; }

    // Get the lines for testing and debugging
    inline const std::pmr::vector<Line>& GetLines() const { return m_Lines; }

    void Rebuild();
#ifndef UNITY
    static void RebuildUniTexts();
#endif

#pragma region RichText Tag
    struct RichTextTag
    {
        int startIndex{ -1 };
        int endIndex{ -1 };

        using value_type = std::optional<std::variant<Color, int, TextStyle, std::string>>;
        value_type value;
    };

    using FormatStack = std::vector<RichTextTag>;

    struct PropertyModifier
    {
        bool isUnderline{false};
        bool isInsideNobr{false};
        std::vector<TextStyle> style;
        std::vector<Color> color;
        std::vector<int> fontSize;
        //std::string string;

        void clear()
        {
            isUnderline = false;
            isInsideNobr = false;
            style.clear();
            color.clear();
            fontSize.clear();
        }
    };
#pragma endregion

private:
    void PreProcessTexts(const uint16* unicodes, int length);
    void ApplyAlignment() noexcept;

    void SetDirty() noexcept;
    void SetDirty(DirtyType dirtyType, bool isDirty) noexcept;
    const bool IsDirty(DirtyType dirtyType) const noexcept
    {
        return m_DirtyFlags.HasBit(static_cast<uint16>(dirtyType));
    }

    void GetTextOffset(Vector2f& offset) noexcept;
    const bool ShouldBreakWord(const uint16 unicode) noexcept;

    void IterateRichTextTags(const int curIndex) noexcept;

    template<RichTextTagType tagType>
    void IterateRichTextTag(const int curIndex) noexcept;
    template<RichTextTagType tagType>
    void ApplyRichTextTag(const RichTextTag& tag) noexcept;
    template<RichTextTagType tagType>
    void RestoreRichTextTag(const RichTextTag& tag) noexcept;

    bool InsertSpace(uint16 unicode, int count = 1) noexcept;
    bool InsertNewLine() noexcept;
    bool InsertCharacter(uint16 unicode) noexcept;
    // layout methods
    bool HandleWordWrap() noexcept;
    bool WrapCurrentWord() noexcept;
    bool LayoutGlyph(const GlyphInfo& glyph, float scale) noexcept;
    bool InsertEllipsisAndStop() noexcept;
    bool StartNewLine() noexcept;
    bool CheckVerticalOverflow() noexcept;

    // New post-processing methods for improved wrapping
    void PostProcessWrapping() noexcept;
    void WrapLine(int lineIndex) noexcept;
    void ApplyOverflowMode(int lineIndex) noexcept;

    void AddGlyphQuad(const Vector2f& bottomLeft, float scale, const Color& color, const GlyphInfo& glyph, const SizedPage* page) noexcept;
    void OffsetGlyphQuad(int startIndex, int endIndex, const Vector2f& offset) noexcept;

    void MarkTextsAsUnused();

    Line& CurrentLine() noexcept { return m_Lines[m_Lines.size() - 1]; }

private:
    bool m_IsActive;
    bool m_IsDirty;

    Bits<uint8> m_Features;
    Bits<uint16> m_DirtyFlags;

    TextOverflow m_HorizontalOverflow;
    TextOverflow m_VerticalOverflow;
    TextAlignment m_HorizontalAlignment;
    TextAlignment m_VerticalAlignment;

    // Base Property
    int16 m_BaseFontSize;
    int16 m_LastFontSize;
    Color m_BaseColor;
    float m_BaseStrokeSize;
    float m_BaseLineSpacing;
    float m_BaseCharSpacing;

    // Text Bounds: equal to RectTransform.width\height in Unity
    GlyphBounds m_Bounds;
    Vector2f m_Pivot;

    UniFont* m_Font;
    UniFont* m_LastUsedFont;

    LocalArenaMemoryResource<32> m_LocalMemResForString;
    LocalArenaMemoryResource<32> m_LocalMemResForVector;
    // @TODO:
    // use std::string with custom allocator to reduce memory allocation
    UniString m_Text;
    // @TODO:
    // use std::vector with custom allocator to reduce memory allocation
    std::pmr::vector<uint16> m_Unicodes;

    std::array<FormatStack, static_cast<size_t>(RichTextTagType::Max)> m_FormatStack{};

    MeshData m_MeshData{};
    // Property Modifier(for rich text)
    PropertyModifier m_PropertyModifier{};

    /// @brief Pen, Letter, Word, Line are all necessary for character layout.
    Pen m_Pen{};
    std::pmr::vector<Letter> m_Letters{};
    std::pmr::vector<Line> m_Lines{};
};

NAMESPACE_END

#endif // __UniTextGenerator_h__