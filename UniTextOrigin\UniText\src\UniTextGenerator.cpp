#include "UniTextGenerator.h"
#include "RenderAPI.h"

#include <algorithm>

NAMESPACE_USE

// complete the definition of s_MemPool in "Utility/TextUtility.h";
SharedMemPool<uint16> UTF16String::s_MemPool{};

// Overload pattern with std::visit
// More details at: https://www.modernescpp.com/index.php/visiting-a-std-variant-with-the-overload-pattern
template<typename ... Ts>
struct Overload : Ts ... {
    using Ts::operator() ...;
};
template<class... Ts> Overload(Ts...)->Overload<Ts...>;

// Not used in Unity engine
#ifndef UNITY
/////////////////////////////////////////////////////////////////
/// Rebuilds
/////////////////////////////////////////////////////////////////
static std::vector<UniTextGenerator*> s_dirtyTexts;
// TODO: collect and free unused
void UniTextGenerator::RebuildUniTexts()
{
    if (s_dirtyTexts.size() > 0)
    {
        for (int i = 0; i < s_dirtyTexts.size(); i++)
        {
            // do rebuild;
            s_dirtyTexts[i]->Rebuild();
        }
        s_dirtyTexts.clear();

        // Fonts UnloadUnusedGlyphs();
        UniFontCache::ForeachFont([](UniFont* font)
            {
                font->UnloadUnusedGlyphs();
            });

        //AtlasManager::GetInstance()->ApplyDirtyTextures();
    }
}
#endif

#if USE_ROUND_POSITION
#include <cmath>
#define ROUND(x) std::round(x)
#else
#define ROUND(x) x
#endif

/////////////////////////////////////////////////////////////////
/// UniTextGenerator
/////////////////////////////////////////////////////////////////
UniTextGenerator::UniTextGenerator()
    :
    m_IsActive(true),
    m_IsDirty(false),
    m_Features(3),              // 1 | 2 = 3 enable richText��kerning by default
    m_DirtyFlags(0),
    m_HorizontalOverflow(TextOverflow::Truncate),
    m_VerticalOverflow(TextOverflow::Overflow),
    m_HorizontalAlignment(TextAlignment::Left),
    m_VerticalAlignment(TextAlignment::Left),
    m_BaseFontSize(28),
    m_BaseColor(Color::white),
    m_BaseStrokeSize(0.0f),
    m_BaseLineSpacing(0.0f),
    m_BaseCharSpacing(0.0f),
    m_Bounds(0, 0, 200, 100),
    m_Pivot(0.5f, 0.5f),
    m_Font(nullptr),
    m_LastUsedFont(nullptr),
    m_Text(&m_LocalMemResForString),
    m_Unicodes(&m_LocalMemResForVector)

{

}

UniTextGenerator::~UniTextGenerator()
{
    //DELETE_PTR(m_Text);
    m_Font = nullptr;
}

#pragma region RichText tags
static const std::array<std::string, 23> kBuiltInColorKey
{
    "red",
    "cyan",
    "blue",
    "darkblue",
    "lightblue",
    "purple",
    "yellow",
    "lime",
    "fuchsia",
    "white",
    "silver",
    "grey",
    "black",
    "orange",
    "brown",
    "maroon",
    "green",
    "olive",
    "navy",
    "teal",
    "aqua",
    "magenta",
    "transparent"
};

static const std::array<Color, 23> kBuiltInColorValue
{
    0xff0000ff,
    0x00ffffff,
    0x0000ffff,
    0x0000a0ff,
    0xadd8e6ff,
    0x800080ff,
    0xffff00ff,
    0x00ff00ff,
    0xff00ffff,
    0xffffffff,
    0xc0c0c0ff,
    0x808080ff,
    0x000000ff,
    0xffa500ff,
    0xa52a2aff,
    0x800000ff,
    0x008000ff,
    0x808000ff,
    0x000080ff,
    0x008080ff,
    0x00ffffff,
    0xff00ffff,
    0x00000000,
};

/// <summary>
/// Strongly related to UniGlyphData.h: RichTextTagType
/// </summary>
static const std::array<std::string, (int)(RichTextTagType::Max)> kBuiltInTagName
{
    "color","style","size","stroke","u","d","quad","material","nobr"
};

// ignore case
static inline auto Comparer = [](const UniStringView& a, const std::string_view& b)
{
    return std::equal(a.begin(), a.end(),
        b.begin(), b.end(),
        [](uint16 c_a, char c_b) {
            return tolower(c_a) == tolower(static_cast<uint16>(c_b));
        });
};

static RichTextTagType TagName2Type(const UniStringView tagName)
{
    for (int i = 0; i < kBuiltInTagName.size(); i++)
    {
        if (Comparer(tagName, kBuiltInTagName[i]))
            return (RichTextTagType)i;
    }

    return RichTextTagType::Unknown;
}

// for local method,
static inline bool IsHex(uint16 c)
{
    return (c >= '0' && c <= '9') || (c >= 'a' && c <= 'f') || (c >= 'A' && c <= 'F');
}

static void HexStringToColor(UniStringView str, size_t bytes, Color& data)
{
    for (size_t i = 0; i < bytes; i++)
    {
        uint8 b;
        char ch = str[2 * i + 0];
        if (ch <= '9')
            b = (ch - '0') << 4;
        else if (ch <= 'Z')
            b = (ch - 'A' + 10) << 4;
        else
            b = (ch - 'a' + 10) << 4;

        ch = str[2 * i + 1];
        if (ch <= '9')
            b |= (ch - '0');
        else if (ch <= 'Z')
            b |= (ch - 'A' + 10);
        else
            b |= (ch - 'a' + 10);

        data[i] = b;
    }
}

static int StringToInt(UniStringView str)
{
    int val = 0;
    for (int i = 0; i < str.size(); i++)
    {
        if (str[i] >= '0' && str[i] <= '9')
        {
            val = val * 10 + (str[i] - '0');
        }
    }
    return val;
}

static inline void ParseValue(UniTextGenerator::RichTextTag& tag, const UniStringView value, RichTextTagType tagType)
{
    switch (tagType)
    {
    case RichTextTagType::Color:
        if (value.at(0) == '#')
        {
            if (value.size() <= 9)
            {
                for (size_t i = 1; i < value.size(); i++)
                {
                    if (!IsHex(value[i])) return;
                }
                /*
                if (value.size() == 4 || value.size() == 5)
                {
                    core::string longcolorstring = "#";
                    for (size_t i = 1; i < colorstring.size(); i++)
                    {
                        longcolorstring += colorstring[i];
                        longcolorstring += colorstring[i];
                    }
                    HexStringToBytes(&longcolorstring[1], longcolorstring.size() / 2, &colorOut);
                }*/
                if (value.size() == 7 || value.size() == 9)
                {
                    Color outColor;
                    HexStringToColor(value.substr(1, value.size() - 1), value.size() / 2, outColor);
                    tag.value = outColor;
                }
            }
            // else invalid "#ffffffff" string
        }
        else
        {
            for (size_t i = 0; i < kBuiltInColorKey.size(); i++)
            {
                if (Comparer(value, kBuiltInColorKey[i]))
                {
                    tag.value = kBuiltInColorValue[i];
                    break;
                }
            }
        }
        break;

    case RichTextTagType::TextStyle:
        if (Comparer(value, "italic"))
        {
            tag.value = TextStyle::Italic;
        }
        else if (Comparer(value, "bold"))
        {
            tag.value = TextStyle::Bold;
        }
        break;

    case RichTextTagType::Size:
        tag.value = StringToInt(value);
        break;

    default:break;
    }
}

static inline int SkipCharacter(const UniStringView str, int startIndex, char ignoreChar)
{
    while (startIndex < str.size())
    {
        if (str[startIndex] == ignoreChar) startIndex++;
    }
    return startIndex;
}

using TagStringValue = std::tuple<UniStringView, std::optional<UniStringView>>;
std::optional<TagStringValue> ReadTagValue(const UniStringView str, int startIndex, int& endIndex)
{
    int equalIndex = -1;
    while (endIndex < str.size())
    {
        auto next_c = str[endIndex++];
        if (next_c == '<')
        {
            // a new tag!? break the current tag
            endIndex = str.size();  // invalid
            break;
        }
        else if (next_c == '=')
        {
            if (equalIndex < 0) equalIndex = endIndex; else equalIndex = 0;
        }
        else if (next_c == '>')
        {
            endIndex--;
            break;
        }
    }

    if (endIndex == str.size()) return std::nullopt;

    if (equalIndex > startIndex && equalIndex <= endIndex)
    {
        // has =
        auto left = str.substr(startIndex, equalIndex - startIndex - 1);
        auto right = str.substr(equalIndex, endIndex - equalIndex);
        return std::make_tuple(left, right);
    }

    return std::make_tuple(str.substr(startIndex, endIndex - startIndex), std::nullopt);
}

void UniTextGenerator::PreProcessTexts(const uint16* unicodes, int length)
{
    MarkTextsAsUnused();

    m_Unicodes.clear();

    if (HasRichText())
    {
        // clear formats
        for (auto& stack : m_FormatStack)
        {
            stack.clear();
        }

        UniStringView str(unicodes, (size_t)length);
        int actualIndex = 0;
        int cur = 0;
        while (cur < str.size())
        {
            auto c = str[cur];
            switch (c)
            {
            case '<':
            {
                auto tagStartIndex = cur + 1;
                auto tagEndIndex = tagStartIndex;
                if (tagEndIndex < str.size() && str[tagEndIndex] == '/')
                {
                    tagStartIndex++;
                    auto sv = ReadTagValue(str, tagStartIndex, tagEndIndex);
                    if (sv.has_value())
                    {
                        auto [left, _] = sv.value();

                        auto tagType = TagName2Type(left);
                        if (tagType != RichTextTagType::Unknown)
                        {
                            for (int it = m_FormatStack[(int)tagType].size() - 1; it >= 0; it--)
                            {
                                auto& tag = m_FormatStack[(int)tagType][it];
                                if (tag.endIndex < 0)
                                {
                                    tag.endIndex = actualIndex;
                                    break;
                                }
                            }
                        }

                        cur = tagEndIndex + 1;
                    }
                    else
                    {
                        m_Unicodes.push_back(c);
                        cur++;
                        actualIndex++;
                    }
                }
                else
                {
                    auto sv = ReadTagValue(str, tagStartIndex, tagEndIndex);
                    if (sv.has_value())
                    {
                        auto [left, right] = sv.value();

                        auto tagType = TagName2Type(left);
                        if (tagType != RichTextTagType::Unknown)
                        {
                            auto& tag = m_FormatStack[(int)tagType].emplace_back(RichTextTag{});
                            tag.startIndex = actualIndex;

                            if (right.has_value())
                            {
                                ParseValue(tag, right.value(), tagType);
                            }
                        }

                        cur = tagEndIndex + 1;
                    }
                    else
                    {
                        m_Unicodes.push_back(c);
                        cur++;
                        actualIndex++;
                    }
                }

                break;
            }
            default:
                m_Unicodes.push_back(c);
                cur++;
                actualIndex++;
                break;
            }
        }
    }
    else
    {
        for (int i = 0; i < length; i++)
        {
            m_Unicodes.push_back(unicodes[i]);
        }
    }
}
#pragma endregion RichText tags

void UniTextGenerator::SetActive(bool active)
{
    if (m_IsActive != active)
    {
        m_IsActive = active;
        if (m_IsActive) SetDirty();
        else { MarkTextsAsUnused(); }
    }
}

void UniTextGenerator::SetText(const char* text)
{
    if (!m_Text.empty() && strcmp(m_Text.c_str(), text) == 0) return;

    m_Text.assign(text);
    // TODO: mem pool::Ensure(m_Text, length);
    //DELETE_PTR(m_Text);
    //size_t length = strlen(text);
    //m_Text = new char[length + 1];
    //strcpy_s(m_Text, length + 1, text);

    SetDirty();
}

void UniTextGenerator::AppendText(const char* text)
{
    m_Text.append(text);
    SetDirty();
}

void UniTextGenerator::SetFont(const char* fontName)
{
    if (m_Font != nullptr && strcmp(m_Font->GetName(), fontName) == 0)
        return;

    //m_Font = std::make_shared(UniFontCache::GetInstance()->GetFont(fontName));
    m_Font = UniFontCache::GetInstance()->GetFont(fontName);
    if (m_Font != nullptr) SetDirty();
}

void UniTextGenerator::SetFont(UniFont* uniFont)
{
    if (m_Font != uniFont)
    {
        m_Font = uniFont;
        if (m_Font != nullptr) SetDirty();
    }
}

void UniTextGenerator::SetFontSize(int16 fontSize)
{
    if (m_BaseFontSize != fontSize)
    {
        m_BaseFontSize = fontSize;
        SetDirty();
    }
}

void UniTextGenerator::SetBounds(float width, float height)
{
    if (m_Bounds.width != width || m_Bounds.height != height)
    {
        m_Bounds.width = width; m_Bounds.height = height;
        SetDirty();
    }
}

void UniTextGenerator::SetHorizontalAlignment(TextAlignment alignment)
{
    if (m_HorizontalAlignment != alignment)
    {
        m_HorizontalAlignment = alignment;
        SetDirty();
    }
}

void UniTextGenerator::SetVerticalAlignment(TextAlignment alignment)
{
    if (m_VerticalAlignment != alignment)
    {
        m_VerticalAlignment = alignment;
        SetDirty();
    }
}

void UniTextGenerator::SetPivot(Vector2f pivot)
{
    m_Pivot = pivot;
}

void UniTextGenerator::SetStyle()
{}

void UniTextGenerator::SetLineSpacing(float lineSpacing)
{
    if (m_BaseLineSpacing != lineSpacing)
    {
        m_BaseLineSpacing = lineSpacing;
        SetDirty();
    }
}

void UniTextGenerator::SetCharacterSpacing(float charSpacing)
{
    if (m_BaseCharSpacing != charSpacing)
    {
        m_BaseCharSpacing = charSpacing;
        SetDirty();
    }
}

void UniTextGenerator::SetStrokeSize(float strokeSize)
{
    if (m_BaseStrokeSize != strokeSize)
    {
        m_BaseStrokeSize = strokeSize;
        SetDirty();
    }
}

void UniTextGenerator::SetDirty() noexcept
{
    if (m_IsDirty) return;

    m_IsDirty = true;

#ifndef UNITY
    s_dirtyTexts.push_back(this);
#endif
}

void UniTextGenerator::SetDirty(DirtyType dirtyType, bool isDirty) noexcept
{
    if (m_DirtyFlags.HasBit(static_cast<uint16>(dirtyType)) == isDirty) return;
    m_DirtyFlags.SetBit(static_cast<uint16>(dirtyType), isDirty);

#ifndef UNITY
    if (isDirty) s_dirtyTexts.push_back(this);
#endif
}

void UniTextGenerator::GetTextOffset(Vector2f& offset) noexcept
{
    offset.x = m_Bounds.x = ROUND(-m_Bounds.width * m_Pivot.x);
    offset.y = m_Bounds.y = ROUND(m_Bounds.height * m_Pivot.y);
}

const bool UniTextGenerator::ShouldBreakWord(const uint16 unicode) noexcept
{
    // Check for common word-breaking characters
    switch (unicode)
    {
        // Spaces, punctuation, and other breaking characters
        case ' ':
        case '\t':
        case '\n':
        case '\r':
        case ',':
        case '.':
        case ';':
        case ':':
        case '!':
        case '?':
        case '/':
        case '\\':
        case '|':
        case '(':
        case ')':
        case '[':
        case ']':
        case '{':
        case '}':
        case '<':
        case '>':
        case '=':
        case '+':
        case '-':
        case '*':
        case '&':
        case '^':
        case '%':
        case '$':
        case '#':
        case '@':
        case '~':
        case '`':
            return true;
    }

    // Latin alphabets (a-z, A-Z)
    if ((unicode >= 'a' && unicode <= 'z') || (unicode >= 'A' && unicode <= 'Z'))
    {
        return false;
    }

    // Numbers (0-9)
    if (unicode >= '0' && unicode <= '9')
    {
        return false;
    }

    // CJK characters - each character is a word
    // CJK Unified Ideographs: 4E00-9FFF
    if (unicode >= 0x4E00 && unicode <= 0x9FFF)
    {
        return true;
    }

    // Japanese Hiragana: 3040-309F
    if (unicode >= 0x3040 && unicode <= 0x309F)
    {
        return true;
    }

    // Japanese Katakana: 30A0-30FF
    if (unicode >= 0x30A0 && unicode <= 0x30FF)
    {
        return true;
    }

    // Korean Hangul: AC00-D7AF
    if (unicode >= 0xAC00 && unicode <= 0xD7AF)
    {
        return true;
    }

    // Default: don't break
    return false;
}

/// <summary>
/// @TODO:
/// Considering using multi-threaded freetype to Load & Render Glyphs
/// then pack them on main thread.
/// This might consume more space, but should be significantly faster.
/// </summary>
void UniTextGenerator::Rebuild()
{
    m_IsDirty = false;
    m_MeshData.Clear();

    if (m_Font == nullptr) return;

    UNITY_PROFILER_START("Preprocess Text");
    UTF16String utfStr(m_Text.c_str());
    PreProcessTexts(utfStr.data, utfStr.length);
    UNITY_PROFILER_END();

    if (m_Unicodes.empty()) return;

    int vertsCount = m_Unicodes.size() * 4;
    if (m_MeshData.vertices.capacity() < vertsCount)
        m_MeshData.Reserve(vertsCount);

    // prepare for text layout
    m_Pen.Reset();
    float characterSize = static_cast<float>(m_BaseFontSize);
    // Step 2 : Load Glyph into Font
    GetTextOffset(m_Pen.offset);

    float ascender = ROUND(m_Font->GetAscender(m_BaseFontSize));
    float descender = ROUND(m_Font->GetDescender(m_BaseFontSize));

    m_Pen.x = m_Pen.offset.x;
    m_Pen.y = m_Pen.offset.y - ascender;
    m_Pen.lineSpacing = ascender - descender; //m_Font.GetLineSpaceing(m_BaseFontSize) * m_LineSpacingFactor;
    m_Pen.minX = characterSize;
    m_Pen.minY = characterSize;

    m_Letters.clear();
    m_Lines.clear();

    // Create an empty line
    auto& line = m_Lines.emplace_back(Line{});
    line.maxFontSize = m_BaseFontSize;
    line.startIndex = 0;

    // Reset property modifier for the real generation
    m_PropertyModifier.clear();

    UNITY_PROFILER_START("Preprocess Unicodes");
    const bool hasRichText = HasRichText();
    const bool hasKerning = HasKerning();

#if USE_THREAD_POOL
    // pre cache glyphs
    for (int i = 0; i < m_Unicodes.size(); i++)
    {
        if (hasRichText) IterateRichTextTags(i);

        auto unicode = m_Unicodes[i];
        switch (unicode)
        {
        case ' ':
            break;
        case '\t':
            break;
        case '\n':
            break;
        case 0x200B:
            break;
        default:
            int fontSize = m_PropertyModifier.fontSize.size() > 0 ? m_PropertyModifier.fontSize.back() : m_BaseFontSize;
            m_Font->SetFontSizeForRendering(fontSize);
            GlyphInfo* glyph;
            m_Font->LoadGlyphAsync(unicode, fontSize, &glyph);
            break;
        }
    }
    m_Font->FinishAsyncJobs();
#endif

    //Word validWord{ -1, 0 };
    for (int i = 0; i < m_Unicodes.size(); i++)
    {
        // iterate and apply rich text tags
        if (hasRichText) IterateRichTextTags(i);

        auto unicode = m_Unicodes[i];
        if (hasKerning)
            m_Pen.x += m_Font->GetKerning(m_Pen.prevUnicode, unicode);

        m_Pen.currentCharIdx = i;

        bool didFit = true;
        switch (unicode)
        {
            case '\t':
                didFit = InsertSpace(unicode, 2);
                break;
            case '\n':
                // new line
                didFit = InsertNewLine();
                break;
            case ' ':
                didFit = InsertSpace(unicode);
                break;
            case 0x200B:
                // Zero width space
                break;
            default:
                didFit = InsertCharacter(unicode);
                break;
        }
        if (!didFit) break;
        m_Pen.prevUnicode = unicode;
    }

    // post process alignment
    ApplyAlignment();

    m_LastUsedFont = m_Font;
    m_LastFontSize = m_BaseFontSize;
    m_DirtyFlags.Set(static_cast<uint16>(DirtyType::None));
    UNITY_PROFILER_END();
}

void UniTextGenerator::ApplyAlignment() noexcept
{
    if (this->m_MeshData.vertices.empty() || m_Lines.empty())
    {
        return;
    }

    // Calculate total text height and line spacing
    float totalHeight = 0.0f;
    float lineSpacing = m_BaseLineSpacing;

    // First pass: calculate total height including line spacing
    for (size_t i = 0; i < m_Lines.size(); ++i)
    {
        totalHeight += m_Lines[i].height;

        // Add line spacing for all lines except the last one
        if (i < m_Lines.size() - 1)
        {
            totalHeight += lineSpacing;
        }
    }

    // Calculate vertical alignment offset
    float verticalOffset = 0.0f;
    switch (m_VerticalAlignment)
    {
        case TextAlignment::Left: // Top alignment
            verticalOffset = 0.0f;
            break;
        case TextAlignment::Center: // Middle alignment
            // revert y axis to match Unity coordinate system.
            verticalOffset = -(m_Bounds.height - totalHeight) * 0.5f;
            break;
        case TextAlignment::Right: // Bottom alignment
            verticalOffset = -(m_Bounds.height - totalHeight);
            break;
    }

    // Apply alignment to each line
    float currentY = verticalOffset;

    for (auto& line : m_Lines)
    {
        // Calculate horizontal alignment offset for this line
        float horizontalOffset = 0.0f;
        switch (m_HorizontalAlignment)
        {
            case TextAlignment::Left:
                horizontalOffset = 0.0f;
                break;
            case TextAlignment::Center:
                horizontalOffset = (m_Bounds.width - line.width) * 0.5f;
                break;
            case TextAlignment::Right:
                horizontalOffset = m_Bounds.width - line.width;
                break;
        }

        // Store the offset in the line for future reference
        line.offset = horizontalOffset;

        // Apply the offsets to all vertices in this line
        for (int idx = line.startIndex; idx <= line.endIndex; idx++)
        {
            if (idx < 0 || idx >= m_Letters.size())
                continue;

            int vertexIdx = m_Letters[idx].vertexIdx;
            if (vertexIdx >= 0 && vertexIdx + 3 < m_MeshData.vertices.size())
            {
                // Apply horizontal and vertical offsets to all 4 vertices of the quad
                for (int i = 0; i < 4; i++)
                {
                    m_MeshData.vertices[vertexIdx + i].x += horizontalOffset;
                    m_MeshData.vertices[vertexIdx + i].y += currentY;
                }
            }
        }

        // Move to the next line position
        currentY += line.height + lineSpacing;
    }
}

void UniTextGenerator::IterateRichTextTags(const int curIndex) noexcept
{
    //for (const auto& tagStack : m_FormatStack)
    //static_for<0, static_cast<int>(RichTextTagType::Max)>()([curIndex](int i)
    //    {
    //        IterateRichTextTag<i>(curIndex);
    //    });
    IterateRichTextTag<RichTextTagType::Color>(curIndex);
    IterateRichTextTag<RichTextTagType::TextStyle>(curIndex);
    IterateRichTextTag<RichTextTagType::Size>(curIndex);
    IterateRichTextTag<RichTextTagType::Stroke>(curIndex);
    IterateRichTextTag<RichTextTagType::Underline>(curIndex);
    IterateRichTextTag<RichTextTagType::Deleteline>(curIndex);
    IterateRichTextTag<RichTextTagType::Quad>(curIndex);
    IterateRichTextTag<RichTextTagType::Material>(curIndex);
    IterateRichTextTag<RichTextTagType::Nobr>(curIndex);
}

template<RichTextTagType tagType>
void UniTextGenerator::IterateRichTextTag(const int curIndex) noexcept
{
    const auto& tagStack = m_FormatStack[static_cast<int>(tagType)];
    const auto stackSize = tagStack.size();
    if (stackSize > 0)
    {
        for (size_t i = 0; i < tagStack.size(); i++)
        {
            const auto& tag = tagStack[i];
            if (curIndex == tag.startIndex)
            {
                // apply tag Value
                ApplyRichTextTag<tagType>(tag);
                break;
            }
            else if (curIndex == tag.endIndex)
            {
                // restore
                RestoreRichTextTag<tagType>(tag);
                break;
            }
        }
    }
}

template<RichTextTagType tagType>
void UniTextGenerator::ApplyRichTextTag(const RichTextTag& tag) noexcept
{
    if constexpr (tagType == RichTextTagType::Color)
    {
        m_PropertyModifier.color.push_back(std::get<Color>(tag.value.value()));
    }
    else if constexpr (tagType == RichTextTagType::TextStyle)
    {
        m_PropertyModifier.style.push_back(std::get<TextStyle>(tag.value.value()));
    }
    else if constexpr (tagType == RichTextTagType::Size)
    {
        m_PropertyModifier.fontSize.push_back(std::get<int>(tag.value.value()));
    }
    else if constexpr (tagType == RichTextTagType::Nobr)
    {
        // No break tag - prevents word wrapping
        m_PropertyModifier.isInsideNobr = true;
    }
    else if constexpr (tagType == RichTextTagType::Underline)
    {
        m_PropertyModifier.isUnderline = true;
    }
}

template<RichTextTagType tagType>
void UniTextGenerator::RestoreRichTextTag(const RichTextTag& tag) noexcept
{
    if constexpr (tagType == RichTextTagType::Color)
    {
        if (m_PropertyModifier.color.size() > 0) m_PropertyModifier.color.pop_back();
    }
    else if constexpr (tagType == RichTextTagType::TextStyle)
    {
        if (m_PropertyModifier.style.size() > 0) m_PropertyModifier.style.pop_back();
    }
    else if constexpr (tagType == RichTextTagType::Size)
    {
        if (m_PropertyModifier.fontSize.size() > 0) m_PropertyModifier.fontSize.pop_back();
    }
    else if constexpr (tagType == RichTextTagType::Nobr)
    {
        // End of no break tag - re-enable word wrapping
        m_PropertyModifier.isInsideNobr = false;
    }
    else if constexpr (tagType == RichTextTagType::Underline)
    {
        m_PropertyModifier.isUnderline = false;
    }
}

bool UniTextGenerator::InsertNewLine() noexcept
{
    // If we have a current word, end it since we're inserting a new line
    if (m_Pen.currentWord.startIndex >= 0)
    {
        m_Pen.currentWord.endIndex = m_Pen.currentCharIdx - 1;
        m_Pen.currentWord.startIndex = -1; // Reset current word
    }

    // Check horizontal overflow settings
    switch (m_HorizontalOverflow)
    {
        case TextOverflow::Overflow:
            // In overflow mode, we don't insert new lines
            return true;
        case TextOverflow::Ellipse:
            // TODO: Insert ellipsis
            return true;
        case TextOverflow::Truncate:
            // Continue with new line
            break;
    }

    // Finalize the current line
    auto& lastLine = CurrentLine();
    lastLine.endIndex = m_Pen.currentCharIdx - 1;

    // Create a new line
    auto& newLine = m_Lines.emplace_back(Line{});
    newLine.startIndex = m_Pen.currentCharIdx + 1; // Start after the newline character

    // Reset pen position for new line
    m_Pen.x = m_Pen.offset.x;
    m_Pen.y -= (m_Pen.lineSpacing + m_BaseLineSpacing);

    // Check if we've exceeded the vertical bounds
    bool isVerticalOverflow = m_Pen.y < (m_Bounds.y - m_Bounds.height);

    if (isVerticalOverflow)
    {
        switch (m_VerticalOverflow)
        {
            case TextOverflow::Overflow:
                // Continue rendering beyond bounds
                break;
            case TextOverflow::Truncate:
                // Stop rendering
                return false;
            case TextOverflow::Ellipse:
                // Insert single horizontal ellipsis character (U+2026) at end of line
                if (m_Pen.currentCharIdx > 0) {
                    // Replace last character with ellipsis
                    m_Unicodes[m_Pen.currentCharIdx-1] = 0x2026;
                    
                    // If we have space, also replace previous character to make room for ellipsis
                    if (m_Pen.currentCharIdx > 1) {
                        m_Unicodes[m_Pen.currentCharIdx-2] = ' ';
                    }
                    
                    // Force rebuild to update layout with ellipsis
                    SetDirty();
                    
                    // Load the ellipsis glyph to ensure proper metrics
                    int fontSize = m_PropertyModifier.fontSize.size() > 0 ?
                        m_PropertyModifier.fontSize.back() : m_BaseFontSize;
                    GlyphInfo* ellipsisGlyph;
                    if (m_Font->LoadGlyph(0x2026, fontSize, &ellipsisGlyph)) {
                        // Update line metrics with ellipsis dimensions
                        CurrentLine().height = std::max(CurrentLine().height,
                            ellipsisGlyph->bounds.height *
                            (static_cast<float>(fontSize) / RoundFontSize(fontSize,
                            m_Font->GetMinFontSize(), m_Font->GetMaxFontSize())));
                    }
                }
                return false;
            default:
                break;
        }
    }

    return true;
}

bool UniTextGenerator::InsertSpace(uint16 unicode, int count) noexcept
{
    // If we have a current word, end it since we're inserting a space
    if (m_Pen.currentWord.startIndex >= 0)
    {
        m_Pen.currentWord.endIndex = m_Pen.currentCharIdx - 1;
        m_Pen.currentWord.startIndex = -1; // Reset current word
    }

    GlyphInfo* glyph;
    int fontSize = m_PropertyModifier.fontSize.size() > 0 ? m_PropertyModifier.fontSize.back() : m_BaseFontSize;
    auto& curLine = CurrentLine();
    curLine.maxFontSize = std::max(fontSize, curLine.maxFontSize);

    if (m_Font->LoadGlyph<false>(unicode, fontSize, &glyph))
    {
        int roundedSize = RoundFontSize(fontSize, m_Font->GetMinFontSize(), m_Font->GetMaxFontSize());
        // Calculate scale for the space character
        float scale = static_cast<float>(fontSize) / roundedSize;

        // Calculate the new pen position after adding the space
        float newX = m_Pen.x + (glyph->advance.x * scale * count) + m_BaseCharSpacing;

        // Check if adding this space would exceed the bounds
        if (newX > (m_Bounds.x + m_Bounds.width))
        {
            // If we're at the beginning of a line, we can't wrap a space
            if (m_Pen.x <= m_Pen.offset.x)
            {
                // Just skip this space
                return true;
            }

            // Otherwise, start a new line instead of adding the space
            return InsertNewLine();
        }

        // Add the space to the current line
        auto& letter = m_Letters.emplace_back(Letter{
            Vector2f(ROUND(glyph->advance.x * scale * count), ROUND(glyph->advance.y * scale)),
            -1 // No visible quad for spaces
        });

        // Update pen position
        m_Pen.x = newX;

        // Update line information
        curLine.endIndex = m_Pen.currentCharIdx;
        curLine.width += letter.advance.x + m_BaseCharSpacing;

        return true;
    }

    return false;
}

bool UniTextGenerator::InsertCharacter(uint16 unicode) noexcept
{
    int fontSize = m_PropertyModifier.fontSize.size() > 0 ? m_PropertyModifier.fontSize.back() : m_BaseFontSize;
    GlyphInfo* glyph;
    m_Font->SetFontSizeForRendering(fontSize);
    
    if (m_Font->LoadGlyph(unicode, fontSize, &glyph))
    {
        int roundedSize = RoundFontSize(fontSize, m_Font->GetMinFontSize(), m_Font->GetMaxFontSize());
        float scale = static_cast<float>(fontSize) / roundedSize;

        // Update word tracking BEFORE layout
        bool shouldBreak = ShouldBreakWord(unicode);
        
        // If this character breaks words and we have a current word, end it
        if (shouldBreak && m_Pen.currentWord.startIndex >= 0)
        {
            m_Pen.currentWord.endIndex = m_Pen.currentCharIdx - 1;
            m_Pen.currentWord.startIndex = -1; // Reset current word
        }
        // If this character doesn't break words and we don't have a current word, start one
        else if (!shouldBreak && m_Pen.currentWord.startIndex < 0)
        {
            m_Pen.currentWord.startIndex = m_Pen.currentCharIdx;
        }

        // Generate mesh for the glyph FIRST
        auto page = m_Font->GetGlyphPage(roundedSize, *glyph);
        if (page != nullptr)
        {
            auto textColor = m_PropertyModifier.color.size() > 0 ? m_PropertyModifier.color.back() : m_BaseColor;
            AddGlyphQuad({ m_Pen.x, m_Pen.y }, scale, textColor, *glyph, page);
        }

        // Then do layout (which may trigger wrapping)
        return LayoutGlyph(*glyph, scale);
    }

    return true;
}

bool UniTextGenerator::HandleWordWrap() noexcept
{
    // Don't wrap inside <nobr> tags
    if (m_PropertyModifier.isInsideNobr) {
        return true;
    }

    auto& curLine = CurrentLine();

    // If no current word or word starts at line beginning (too long to wrap)
    if (m_Pen.currentWord.startIndex < 0 || 
        m_Pen.currentWord.startIndex == curLine.startIndex) {
        
        // Just start a new line at current position
        return StartNewLine();
    }

    // Move current word to new line
    return WrapCurrentWord();
}

bool UniTextGenerator::WrapCurrentWord() noexcept
{
    if (m_Pen.currentWord.startIndex < 0) {
        return StartNewLine(); // No word to wrap
    }

    auto& curLine = CurrentLine();
    
    // Calculate how many characters belong to the current word
    int wordCharCount = m_Pen.currentCharIdx - m_Pen.currentWord.startIndex;
    
    if (wordCharCount <= 0) {
        return StartNewLine();
    }

    // Remove word's mesh data from current line
    int verticesPerChar = 4;
    int wordVertexCount = wordCharCount * verticesPerChar;
    
    // Remove vertices, colors, and UVs for the word
    if (m_MeshData.vertices.size() >= wordVertexCount) {
        m_MeshData.vertices.resize(m_MeshData.vertices.size() - wordVertexCount);
        m_MeshData.colors.resize(m_MeshData.colors.size() - wordVertexCount);
        m_MeshData.uvs.resize(m_MeshData.uvs.size() - wordVertexCount);
    }

    // Calculate word width and remove it from line
    float wordWidth = 0.0f;
    for (int i = 0; i < wordCharCount && (m_Letters.size() - wordCharCount + i) < m_Letters.size(); ++i) {
        const auto& letter = m_Letters[m_Letters.size() - wordCharCount + i];
        wordWidth += letter.advance.x + m_BaseCharSpacing;
    }

    // Remove word letters
    m_Letters.resize(m_Letters.size() - wordCharCount);
    
    // Update current line
    curLine.width -= wordWidth;
    curLine.endIndex = m_Pen.currentWord.startIndex - 1;
    
    // Update pen position
    m_Pen.x -= wordWidth;

    // Start new line
    if (!StartNewLine()) {
        return false;
    }

    // Reset pen position and character index to re-process the word
    m_Pen.x = m_Pen.offset.x;
    m_Pen.currentCharIdx = m_Pen.currentWord.startIndex;
    
    // Keep the word start index so we can continue building it
    // Don't reset it here - let the character processing continue

    return true;
}

bool UniTextGenerator::StartNewLine() noexcept
{
    auto& curLine = CurrentLine();
    curLine.endIndex = m_Pen.currentCharIdx - 1;

    // Create new line
    auto& newLine = m_Lines.emplace_back(Line{});
    newLine.startIndex = m_Pen.currentCharIdx;

    // Reset pen position
    m_Pen.x = m_Pen.offset.x;
    m_Pen.y -= (m_Pen.lineSpacing + m_BaseLineSpacing);

    // Check vertical overflow
    return CheckVerticalOverflow();
}

bool UniTextGenerator::CheckVerticalOverflow() noexcept
{
    bool isVerticalOverflow = m_Pen.y < (m_Bounds.y - m_Bounds.height);
    
    if (!isVerticalOverflow) {
        return true; // Continue processing
    }

    switch (m_VerticalOverflow)
    {
    case TextOverflow::Overflow:
        return true; // Continue beyond bounds
    case TextOverflow::Truncate:
        return false; // Stop processing
    case TextOverflow::Ellipse:
        return InsertEllipsisAndStop();
    default:
        return true;
    }
}

bool UniTextGenerator::InsertEllipsisAndStop() noexcept
{
    // Only insert ellipsis if we have space and characters
    if (m_Letters.empty() || m_Pen.currentCharIdx == 0) {
        return false;
    }

    // Get ellipsis glyph
    int fontSize = m_PropertyModifier.fontSize.size() > 0 ? 
        m_PropertyModifier.fontSize.back() : m_BaseFontSize;
    
    GlyphInfo* ellipsisGlyph;
    if (!m_Font->LoadGlyph(0x2026, fontSize, &ellipsisGlyph)) {
        return false; // Can't load ellipsis, just truncate
    }

    float scale = static_cast<float>(fontSize) / 
        RoundFontSize(fontSize, m_Font->GetMinFontSize(), m_Font->GetMaxFontSize());
    float ellipsisWidth = ellipsisGlyph->advance.x * scale;

    // Remove characters from the end until ellipsis fits
    while (!m_Letters.empty() && 
           (m_Pen.x + ellipsisWidth) > (m_Bounds.x + m_Bounds.width))
    {
        auto& lastLetter = m_Letters.back();
        
        // Remove last character's mesh data
        if (lastLetter.vertexIdx >= 0) {
            // Remove 4 vertices, 4 colors, 4 UVs for the quad
            m_MeshData.vertices.resize(m_MeshData.vertices.size() - 4);
            m_MeshData.colors.resize(m_MeshData.colors.size() - 4);
            m_MeshData.uvs.resize(m_MeshData.uvs.size() - 4);
        }

        // Update pen position
        m_Pen.x -= (lastLetter.advance.x + m_BaseCharSpacing);
        
        // Remove the letter
        m_Letters.pop_back();
        
        // Update current line
        auto& curLine = CurrentLine();
        curLine.width -= (lastLetter.advance.x + m_BaseCharSpacing);
        curLine.endIndex--;
    }

    // Insert ellipsis
    auto page = m_Font->GetGlyphPage(
        RoundFontSize(fontSize, m_Font->GetMinFontSize(), m_Font->GetMaxFontSize()),
        *ellipsisGlyph);
    
    if (page != nullptr) {
        Color textColor = m_PropertyModifier.color.size() > 0 ? 
            m_PropertyModifier.color.back() : m_BaseColor;
        AddGlyphQuad({m_Pen.x, m_Pen.y}, scale, textColor, *ellipsisGlyph, page);
    }

    // Add ellipsis letter
    m_Letters.emplace_back(Letter{
        Vector2f(ROUND(ellipsisWidth), 0),
        static_cast<int>(m_MeshData.vertices.size() - 4)
    });

    // Update final line metrics
    auto& curLine = CurrentLine();
    curLine.width += ellipsisWidth;
    curLine.endIndex = m_Pen.currentCharIdx;

    return false; // Stop processing after ellipsis
}

bool UniTextGenerator::LayoutGlyph(const GlyphInfo& glyph, float scale) noexcept
{
    auto& curLine = CurrentLine();

    // Calculate glyph dimensions
    float glyphWidth = glyph.advance.x * scale;
    float glyphHeight = glyph.bounds.height * scale;
    float newX = m_Pen.x + glyphWidth + m_BaseCharSpacing;

    // Check if glyph exceeds horizontal bounds BEFORE adding it
    bool exceedsBounds = newX > (m_Bounds.x + m_Bounds.width);
    
    if (exceedsBounds && m_HorizontalOverflow == TextOverflow::Wrap)
    {
        // Try to wrap the current word if we have one
        if (m_Pen.currentWord.startIndex >= 0)
        {
            if (!HandleWordWrap()) {
                return false; // Vertical bounds exceeded
            }
            // Recalculate position after wrap
            newX = m_Pen.x + glyphWidth + m_BaseCharSpacing;
        }
        else
        {
            // No current word, just start a new line
            if (!StartNewLine()) {
                return false;
            }
            newX = m_Pen.x + glyphWidth + m_BaseCharSpacing;
        }
    }
    else if (exceedsBounds)
    {
        // Handle other overflow modes
        switch (m_HorizontalOverflow)
        {
        case TextOverflow::Overflow:
            // Allow overflow - continue with layout
            break;
        case TextOverflow::Truncate:
            return false; // Stop processing
        case TextOverflow::Ellipse:
            return InsertEllipsisAndStop();
        }
    }

    // Create letter entry
    auto& letter = m_Letters.emplace_back(Letter{
        Vector2f(ROUND(glyphWidth), ROUND(glyph.advance.y * scale)),
        static_cast<int>(m_MeshData.vertices.size()) // Current vertex count before adding quad
    });

    // Update pen and line metrics
    m_Pen.x = newX;

    // Update line information
    curLine.endIndex = m_Pen.currentCharIdx;
    curLine.width += glyphWidth + m_BaseCharSpacing;
    curLine.height = std::max(curLine.height, glyphHeight);

    // Update bounding box
    float glyphBottom = glyph.bounds.y - glyphHeight;
    float glyphTop = glyph.bounds.y;
    m_Pen.minY = std::min(glyphBottom, m_Pen.minY);
    m_Pen.maxY = std::max(glyphTop, m_Pen.maxY);
    m_Pen.lineSpacing = std::max(m_Pen.lineSpacing, m_Pen.maxY - m_Pen.minY);

    return true;
}

void UniTextGenerator::AddGlyphQuad(const Vector2f& offset, float scale, const Color& color, const GlyphInfo& glyph, const SizedPage* page) noexcept
{
    const int padding = m_Font->GetPadding();

  //  float w_scale = (scale - 1.0f) * glyph.bounds.width;
    const float h_scale = (scale - 1.0f) * glyph.bounds.height;
    const float spread_scale = static_cast<float>(glyph.spreadSize) * (scale - 1.0f);

    // only scale on top|right, don't change bottom|left
    const float left = glyph.bounds.x - padding + spread_scale;
    const float right = glyph.bounds.x + glyph.bounds.width * scale + padding - spread_scale;
    const float top = glyph.bounds.y  + h_scale + padding - spread_scale;
    const float bottom = glyph.bounds.y - glyph.bounds.height - padding + spread_scale;

    // this is the unity style quad
    // 0 -- 1
    // |    |
    // 3 -- 2
    //auto vert_bl = Vector2f{ offset.x + left, offset.y + bottom };
    //auto vert_br = Vector2f{ offset.x + right, vert_bl.y };
    //auto vert_tr = Vector2f{ vert_br.x, offset.y + top };
    //auto vert_tl = Vector2f{ vert_bl.x, vert_tr.y };
    //m_MeshData.vertices.push_back(vert_tl);
    //m_MeshData.vertices.push_back(vert_tr);
    //m_MeshData.vertices.push_back(vert_br);
    //m_MeshData.vertices.push_back(vert_bl);
    const auto& vert_tl = m_MeshData.vertices.emplace_back(Vector2f{ offset.x + left, offset.y + top });
    const auto& vert_tr = m_MeshData.vertices.emplace_back(Vector2f{ offset.x + right, vert_tl.y });
    const auto& vert_br = m_MeshData.vertices.emplace_back(Vector2f{ vert_tr.x, offset.y + bottom });
    m_MeshData.vertices.emplace_back(Vector2f{ vert_tl.x, vert_br.y });

    m_MeshData.colors.emplace_back(color);
    m_MeshData.colors.emplace_back(color);
    m_MeshData.colors.emplace_back(color);
    m_MeshData.colors.emplace_back(color);

    const float invWidth = 1.0f / page->GetTexture()->GetWidth();
    const float invHeight = 1.0f / page->GetTexture()->GetHeight();

    const auto& uv_tl = m_MeshData.uvs.emplace_back((glyph.rect.x - padding + spread_scale) * invWidth, (glyph.rect.y - padding - spread_scale) * invHeight, static_cast<float>(page->textureEntry.textureIndex));
    const auto& uv_tr = m_MeshData.uvs.emplace_back((glyph.rect.x + glyph.rect.width + padding - spread_scale) * invWidth, uv_tl.y, static_cast<float>(page->textureEntry.textureIndex));
    const auto& uv_br = m_MeshData.uvs.emplace_back(uv_tr.x, (glyph.rect.y + glyph.rect.height + padding + spread_scale) * invHeight, static_cast<float>(page->textureEntry.textureIndex));
    // uv_bl
    m_MeshData.uvs.emplace_back(uv_tl.x, uv_br.y, static_cast<float>(page->textureEntry.textureIndex));
}

void UniTextGenerator::OffsetGlyphQuad(int startIndex, int endIndex, const Vector2f& offset) noexcept
{

}

void UniTextGenerator::MarkTextsAsUnused()
{
    if (m_Unicodes.size() > 0)
    {
        auto usingFont = m_LastUsedFont != nullptr ? m_LastUsedFont : m_Font;
        if (usingFont != nullptr)
        {
            m_PropertyModifier.clear();
            const bool hasRichText = HasRichText();
            for (int i = 0; i < m_Unicodes.size(); i++)
            {
                if (hasRichText) IterateRichTextTags(i);

                int fontSize = m_PropertyModifier.fontSize.size() > 0 ? m_PropertyModifier.fontSize.back() : m_LastFontSize;
                usingFont->UnloadGlyph(m_Unicodes[i], fontSize);
            }
        }
    }
}